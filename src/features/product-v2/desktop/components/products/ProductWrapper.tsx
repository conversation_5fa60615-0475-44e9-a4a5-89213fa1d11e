'use client'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'
import { useGetInfiniteProductsV2 } from '@/features/product-v2/hooks/query/useGetInfiniteProductsV2'
import { useLocale } from 'next-intl'
import FeaturedProductList from './FeaturedProductList'
import ProductList from './ProductList'
import ProductSkeleton from './ProductSkeleton'
import { useState, useEffect } from 'react'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'

const ProductWrapper: React.FC = () => {
  const locale = useLocale()
  const { getAllSearchQueries } = useSearchQuery()

  const [typeFilter, setTypeFilter] = useState<ProductV2TypeEnum[]>([ProductV2TypeEnum.MEDICINE])

  const { category } = getAllSearchQueries()
  // Update typeFilter when category changes in URL
  useEffect(() => {
    if (category && Object.values(ProductV2TypeEnum).includes(category as ProductV2TypeEnum)) {
      setTypeFilter([category as ProductV2TypeEnum])
    } else {
      // Default to MEDICINE if no valid category in URL
      setTypeFilter([ProductV2TypeEnum.MEDICINE])
    }
  }, [category])

  const { isLoading, productsV2: featuredProduct } = useGetInfiniteProductsV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 12,
      categories: [],
      where: {
        and: [
          {
            featured: {
              equals: true,
            },
          },
          {
            type: {
              in: typeFilter,
            },
          },
        ],
      },
    },
  })

  const { isLoading: isProductListLoading, productsV2: productList } = useGetInfiniteProductsV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 20,
      categories: [],
      where: {
        and: [
          {
            type: {
              in: typeFilter,
            },
          },
        ],
      },
    },
  })

  const dataProductFeatured = featuredProduct?.pages[0]?.docs
  const dataProductList = productList?.pages[0]?.docs

  return (
    <>
      <FeaturedProductList featureProduct={dataProductFeatured ?? []} isLoading={isLoading} />
      <ProductList isLoading={isProductListLoading} productListData={dataProductList ?? []} />
    </>
  )
}

export default ProductWrapper
