import Image from 'next/image'

import BabyFaceIcon from '@/assets/icons/baby-face.svg'
import { useGetCategoriesV2 } from '@/features/product-v2/hooks/query/useGetCategoriesV2'
import { useLocale } from 'next-intl'
import { ProductV2TypeEnum } from '@/features/product-v2/enums'

const BodyParts: React.FC = () => {
  const locale = useLocale()
  const {
    categoriesV2,
    isGetCategoriesV2Error,
    isGetProductsV2Loading,
    fetchNextPage,
    hasNextPage,
  } = useGetCategoriesV2({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: locale ?? 'vi',
      limit: 12,
      categories: [],
      where: {
        and: [
          {
            featured: {
              equals: true,
            },
          },
          {
            type: {
              in: [ProductV2TypeEnum.MEDICINE],
            },
          },
        ],
      },
    },
  })

  return (
    <div className="mt-3 grid grid-cols-6 gap-3">
      <div className="flex cursor-pointer flex-col items-center justify-center gap-3 rounded-md border border-transparent bg-neutral-50 p-3 hover:border-primary-500 hover:text-primary-500">
        <Image src={BabyFaceIcon} alt={'baby-face'} width={36} height={36} />
        <span className="typo-body-6">Đầu</span>
      </div>
    </div>
  )
}

export default BodyParts
