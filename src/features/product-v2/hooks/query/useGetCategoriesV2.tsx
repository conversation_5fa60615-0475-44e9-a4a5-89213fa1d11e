import { Params } from '@/types/http.type'
import { InfiniteData, UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { categoriesV2QueryKeys, productV2QueryKeys } from './queryKeys'
import { Product } from '@/payload-types'
import { productV2Service } from '../../services/product-v2.service'

type CategoriesQueryConfig = Omit<
  UseInfiniteQueryOptions<
    PaginatedDocs<any>,
    Error,
    InfiniteData<PaginatedDocs<any>>,
    PaginatedDocs<any>,
    (string | Params)[]
  >,
  'queryFn' | 'queryKey' | 'getNextPageParam' | 'initialPageParam'
>

interface useGetCategoriesV2Props {
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: CategoriesQueryConfig
}

export const useGetCategoriesV2 = ({
  params = {},
  options = {},
  config = {},
}: useGetCategoriesV2Props = {}) => {
  const {
    isError: isGetCategoriesV2Error,
    isFetching: isGetProductsV2Loading,
    data: categoriesV2,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: [categoriesV2QueryKeys['product-categories'].base(), params],
    queryFn: async ({ pageParam = 1 }) => {
      return productV2Service.getCategoriesV2({
        params: {
          ...params,
          page: pageParam,
        },
        options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    initialPageParam: 1,
    ...config,
  })

  return {
    isGetCategoriesV2Error,
    isGetProductsV2Loading,
    categoriesV2,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
